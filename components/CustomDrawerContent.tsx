import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Alert,
} from "react-native";
import { DrawerContentComponentProps } from "@react-navigation/drawer";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { router } from "expo-router";
import { Svg, Path, Circle } from "react-native-svg";

import { rtlStyle } from "@/utils/rtl";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Colors } from "@/constants/Colors";

// Icon Components
const HomeIcon = ({ color = "#6B7280", size = 24 }) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
  >
    <Path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
    <Path d="M9 22V12h6v10" />
  </Svg>
);

const SettingsIcon = ({ color = "#6B7280", size = 24 }) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
  >
    <Path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
    <Circle cx="12" cy="12" r="3" />
  </Svg>
);

const UserIcon = ({ color = "#6B7280", size = 24 }) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
  >
    <Path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
    <Circle cx="12" cy="7" r="4" />
  </Svg>
);

const InfoIcon = ({ color = "#6B7280", size = 24 }) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
  >
    <Circle cx="12" cy="12" r="10" />
    <Path d="M12 16v-4" />
    <Path d="M12 8h.01" />
  </Svg>
);

const LogoutIcon = ({ color = "#EF4444", size = 24 }) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
  >
    <Path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
    <Path d="M16 17l5-5-5-5" />
    <Path d="M21 12H9" />
  </Svg>
);

interface DrawerItemProps {
  icon: React.ReactNode;
  label: string;
  onPress: () => void;
  isActive?: boolean;
  colorScheme: "light" | "dark";
}

const DrawerItem: React.FC<DrawerItemProps> = ({
  icon,
  label,
  onPress,
  isActive,
  colorScheme,
}) => (
  <TouchableOpacity
    style={[
      styles.drawerItem,
      isActive && { backgroundColor: Colors[colorScheme].tint + "20" },
    ]}
    onPress={onPress}
  >
    <View style={styles.iconContainer}>{icon}</View>
    <Text
      style={[
        styles.drawerItemText,
        {
          color: isActive ? Colors[colorScheme].tint : Colors[colorScheme].text,
        },
      ]}
    >
      {label}
    </Text>
  </TouchableOpacity>
);

export function CustomDrawerContent(props: DrawerContentComponentProps) {
  const colorScheme = useColorScheme() ?? "light";
  const insets = useSafeAreaInsets();

  const handleLogout = () => {
    Alert.alert(
      "خروج از حساب",
      "آیا مطمئن هستید که می‌خواهید از حساب کاربری خود خارج شوید؟",
      [
        {
          text: "لغو",
          style: "cancel",
        },
        {
          text: "خروج",
          style: "destructive",
          onPress: () => {
            // Implement logout logic here
            Alert.alert("خروج", "با موفقیت خارج شدید");
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView
      style={[
        styles.container,
        { backgroundColor: Colors[colorScheme].background },
      ]}
    >
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top }]}>
        <View style={styles.profileSection}>
          <View
            style={[
              styles.avatar,
              { backgroundColor: Colors[colorScheme].tint },
            ]}
          >
            <UserIcon color="#FFFFFF" size={32} />
          </View>
          <View style={styles.profileInfo}>
            <Text
              style={[styles.userName, { color: Colors[colorScheme].text }]}
            >
              کاربر آزمون
            </Text>
            <Text
              style={[
                styles.userEmail,
                { color: Colors[colorScheme].tabIconDefault },
              ]}
            >
              <EMAIL>
            </Text>
          </View>
        </View>
      </View>

      {/* Menu Items */}
      <ScrollView
        style={styles.menuContainer}
        showsVerticalScrollIndicator={false}
      >
        <DrawerItem
          icon={<HomeIcon color={Colors[colorScheme].text} />}
          label="خانه"
          onPress={() => router.push("/(drawer)/(tabs)")}
          colorScheme={colorScheme}
        />

        <DrawerItem
          icon={<HomeIcon color={Colors[colorScheme].text} />}
          onPress={() => router.push("/screens/MajorsList")}
          colorScheme={colorScheme}
          label="رشته‌ها"
        />
        <DrawerItem
          icon={<InfoIcon color={Colors[colorScheme].text} />}
          onPress={() => router.push("/screens/CoursesList")}
          colorScheme={colorScheme}
          label="درس‌ها"
        />
        {/* <DrawerItem
          icon={<InfoIcon color={Colors[colorScheme].text} />}
          onPress={() => router.push("/screens/ChaptersList")}
          colorScheme={colorScheme}
          label="فصل‌ها"
        /> */}
        {/* <DrawerItem
          icon={<InfoIcon color={Colors[colorScheme].text} />}
          onPress={() => router.push("/screens/MajorCoursesList")}
          colorScheme={colorScheme}
          label="MajorCoursesList"
        /> */}
        <DrawerItem
          icon={<InfoIcon color={Colors[colorScheme].text} />}
          onPress={() => router.push("/screens/QuestionsList")}
          colorScheme={colorScheme}
          label="سوال‌ها"
        />
        {/* <DrawerItem
          icon={<InfoIcon color={Colors[colorScheme].text} />}
          onPress={() => router.push("/screens/OptionsList")}
          colorScheme={colorScheme}
          label="گزینه‌ها"
        /> */}
        <DrawerItem
          icon={<InfoIcon color={Colors[colorScheme].text} />}
          onPress={() => router.push("/screens/UsersList")}
          colorScheme={colorScheme}
          label="کاربران"
        />
        <DrawerItem
          icon={<InfoIcon color={Colors[colorScheme].text} />}
          onPress={() => router.push("/screens/SessionsList")}
          colorScheme={colorScheme}
          label="آزمون‌ها"
        />

        {/* Divider */}
        <View
          style={[
            styles.divider,
            { backgroundColor: Colors[colorScheme].tabIconDefault },
          ]}
        />
        <DrawerItem
          icon={<UserIcon color={Colors[colorScheme].text} />}
          label="پروفایل"
          onPress={() => router.push("/screens/Profile")}
          colorScheme={colorScheme}
        />

        <DrawerItem
          icon={<SettingsIcon color={Colors[colorScheme].text} />}
          label="تنظیمات"
          onPress={() => router.push("/screens/Settings")}
          colorScheme={colorScheme}
        />

        <DrawerItem
          icon={<InfoIcon color={Colors[colorScheme].text} />}
          label="درباره ما"
          onPress={() => router.push("/screens/About")}
          colorScheme={colorScheme}
        />

        <DrawerItem
          icon={<LogoutIcon />}
          label="خروج از حساب"
          onPress={handleLogout}
          colorScheme={colorScheme}
        />
      </ScrollView>

      {/* Footer */}
      <View
        style={[
          styles.footer,
          { borderTopColor: Colors[colorScheme].tabIconDefault },
        ]}
      >
        <Text
          style={[
            styles.footerText,
            { color: Colors[colorScheme].tabIconDefault },
          ]}
        >
          نسخه ۰.۰.۱
        </Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E7EB",
  },
  profileSection: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: "center",
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    ...rtlStyle.marginLeft(16),
  },
  profileInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: "bold",
    fontFamily: "Vazirmatn",
    textAlign: rtlStyle.textAlign.start,
  },
  userEmail: {
    fontSize: 14,
    marginTop: 4,
    textAlign: rtlStyle.textAlign.start,
  },
  menuContainer: {
    flex: 1,
    paddingTop: 20,
  },
  drawerItem: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginHorizontal: 12,
    borderRadius: 12,
  },
  iconContainer: {
    ...rtlStyle.marginLeft(16),
  },
  drawerItemText: {
    fontSize: 16,
    fontFamily: "Vazirmatn",
    textAlign: rtlStyle.textAlign.start,
  },
  divider: {
    height: 1,
    marginHorizontal: 20,
    marginVertical: 16,
  },
  footer: {
    marginTop: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
  },
  footerText: {
    fontSize: 12,
    textAlign: "center",
    fontFamily: "Vazirmatn",
  },
});
