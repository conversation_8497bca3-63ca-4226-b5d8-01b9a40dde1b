import React, { useEffect, useState } from 'react';
import { FlatList, ActivityIndicator, StyleSheet, Pressable, Alert, RefreshControl } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { User, userService } from '@/services/userService';
import { Link, router } from 'expo-router';

export default function UsersListScreen() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await userService.getUsers();
      setUsers(data);
    } catch (err) {
      setError('Failed to fetch users.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      setError(null);
      const data = await userService.getUsers();
      setUsers(data);
    } catch (err) {
      setError('Failed to refresh users.');
      console.error(err);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  const handleDeleteUser = async (id: string) => {
    Alert.alert(
      'Delete User',
      'Are you sure you want to delete this user?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: async () => {
            try {
              await userService.deleteUser(id);
              Alert.alert('Success', 'User deleted successfully!');
              loadUsers(); // Refresh the list
            } catch (error) {
              Alert.alert('Error', 'Failed to delete user. Please try again.');
              console.error('Failed to delete user:', error);
            }
          },
          style: 'destructive',
        },
      ],
      { cancelable: true }
    );
  };

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading users...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Users</ThemedText>
      <Link href="/screens/UserAdd" asChild>
        <Pressable style={styles.addButton}>
          <ThemedText style={styles.addButtonText}>Add New User</ThemedText>
        </Pressable>
      </Link>
      {users.length === 0 ? (
        <ThemedText style={styles.noDataText}>No users found.</ThemedText>
      ) : (
        <FlatList
          testID="users-flatlist"
          data={users}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <ThemedView style={styles.userItem}>
              <Pressable onPress={() => router.push(`/UserDetails?id=${item.id}`)}>
                <ThemedText style={styles.userName}>{item.first_name} {item.last_name}</ThemedText>
                <ThemedText>Email: {item.email}</ThemedText>
                <ThemedText>Mobile: {item.mobile}</ThemedText>
                <ThemedText>Role: {item.role}</ThemedText>
              </Pressable>
              <ThemedView style={styles.actions}>
                <Link href={`/screens/UserEdit?id=${item.id}`} asChild>
                  <Pressable style={styles.actionButton}>
                    <ThemedText style={styles.actionButtonText}>ویرایش</ThemedText>
                  </Pressable>
                </Link>
                <Pressable
                  style={[styles.actionButton, styles.deleteButton]}
                  onPress={() => handleDeleteUser(item.id)}
                >
                  <ThemedText style={styles.actionButtonText}>Delete</ThemedText>
                </Pressable>
              </ThemedView>
            </ThemedView>
          )}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#007bff']}
              tintColor="#007bff"
            />
          }
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  addButton: {
    backgroundColor: '#007bff',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginBottom: 16,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  userItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
  },
  userName: {
    fontSize: 16,
  },
  actions: {
    flexDirection: 'row',
  },
  actionButton: {
    backgroundColor: '#28a745',
    padding: 8,
    borderRadius: 5,
    marginLeft: 8,
  },
  actionButtonText: {
    color: '#fff',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
  noDataText: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 20,
  },
  deleteButton: {
    backgroundColor: '#dc3545',
  },
});
