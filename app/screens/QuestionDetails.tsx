import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Question, questionService } from '@/services/questionService';
import { useLocalSearchParams } from 'expo-router';

export default function QuestionDetailsScreen() {
  const { id } = useLocalSearchParams();
  const [question, setQuestion] = useState<Question | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchQuestion = async () => {
        try {
          const data = await questionService.getQuestion(id as string);
          setQuestion(data);
        } catch (err) {
          setError('Failed to fetch question details.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchQuestion();
    } else {
      setError('Question ID not provided.');
      setLoading(false);
    }
  }, [id]);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading question details...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  if (!question) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText>Question not found.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Question Details</ThemedText>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>ID:</ThemedText>
        <ThemedText>{question.id}</ThemedText>
      </ThemedView>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>متن سوال:</ThemedText>
        <ThemedText>{question.question_text}</ThemedText>
      </ThemedView>
      {question.createdAt && (
        <ThemedView style={styles.detailItem}>
          <ThemedText style={styles.label}>Created At:</ThemedText>
          <ThemedText>
            {new Date(question.createdAt).toLocaleString()}
          </ThemedText>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  label: {
    fontWeight: 'bold',
    marginRight: 5,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
