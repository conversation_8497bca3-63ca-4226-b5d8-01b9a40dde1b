import React, { useEffect, useState } from "react";
import {
  FlatList,
  ActivityIndicator,
  Text,
  StyleSheet,
  Pressable,
  RefreshControl,
} from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Question, questionService } from "@/services/questionService";
import { Link } from "expo-router";

export default function QuestionsListScreen() {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadQuestions = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await questionService.getQuestions();
      setQuestions(data);
    } catch (err) {
      setError("Failed to fetch questions.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      setError(null);
      const data = await questionService.getQuestions();
      setQuestions(data);
    } catch (err) {
      setError("Failed to refresh questions.");
      console.error(err);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadQuestions();
  }, []);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading questions...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Questions</ThemedText>
      <Link href="/screens/QuestionAdd" asChild>
        <Pressable style={styles.addButton}>
          <ThemedText style={styles.addButtonText}>Add New Question</ThemedText>
        </Pressable>
      </Link>
      {questions.length === 0 ? (
        <ThemedText style={styles.noDataText}>No questions found.</ThemedText>
      ) : (
        <FlatList
          testID="questions-flatlist"
          data={questions}
          keyExtractor={(item: Question) => item.id}
          renderItem={({ item }: { item: Question }) => (
            <ThemedView style={styles.questionItem}>
              <Link href={`/screens/QuestionDetails?id=${item.id}`} asChild>
                <Pressable>
                  <ThemedText style={styles.subtitle}>#{item.id}</ThemedText>
                  <Text
                    numberOfLines={1}
                    ellipsizeMode="head"
                    style={styles.questionName}
                  >
                    {item.question_text}
                  </Text>
                </Pressable>
              </Link>
              <ThemedView style={styles.actions}>
                <Link href={`/screens/QuestionEdit?id=${item.id}`} asChild>
                  <Pressable style={styles.actionButton}>
                    <ThemedText style={styles.actionButtonText}>
                      ویرایش
                    </ThemedText>
                  </Pressable>
                </Link>
                <Link href={`/screens/OptionsList?questinId=${item.id}`} asChild>
                  <Pressable style={styles.actionButton}>
                    <ThemedText style={styles.actionButtonText}>
                      گزینه‌ها
                    </ThemedText>
                  </Pressable>
                </Link>
                {/* Delete functionality would be implemented here */}
              </ThemedView>
            </ThemedView>
          )}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={["#007bff"]}
              tintColor="#007bff"
            />
          }
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 16,
  },
  addButton: {
    backgroundColor: "#007bff",
    padding: 10,
    borderRadius: 5,
    alignItems: "center",
    marginBottom: 16,
  },
  addButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },
  questionItem: {
    flexDirection: "column",
    // justifyContent: "space",
    alignItems: "left",
    padding: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
  },
  questionName: {
    fontSize: 16,
  },
  actions: {
    flexDirection: "row",
  },
  actionButton: {
    backgroundColor: "#28a745",
    padding: 8,
    borderRadius: 5,
    marginLeft: 8,
  },
  actionButtonText: {
    color: "#fff",
  },
  errorText: {
    color: "red",
    fontSize: 16,
  },
  noDataText: {
    fontSize: 18,
    textAlign: "center",
    marginTop: 20,
  },
});
